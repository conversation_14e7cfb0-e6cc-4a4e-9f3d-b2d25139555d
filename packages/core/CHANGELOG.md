# @betswirl/sdk-core

## 0.1.2

### Patch Changes

- [`a8f5653`](https://github.com/BetSwirl/sdk/commit/a8f565323c65253a25af73f5a39cf45268e83807) Thanks [@Kinco-dev](https://github.com/Kinco-dev)! - Add getWaitRollEventData

- [`92fb0c2`](https://github.com/BetSwirl/sdk/commit/92fb0c2537deeae3502ceb507feed0bb8471730b) Thanks [@Kinco-dev](https://github.com/Kinco-dev)! - Update fetchFreebetCampaigns return type

- [`0f1dc7d`](https://github.com/BetSwirl/sdk/commit/0f1dc7dfdf4434c84c531c6999b5f8b2d635a929) Thanks [@Kinco-dev](https://github.com/Kinco-dev)! - Add place freebet

- [`d040470`](https://github.com/BetSwirl/sdk/commit/d0404703137aa47ad934f1e1b32901ddc0c889ff) Thanks [@Kinco-dev](https://github.com/Kinco-dev)! - Add getPlaceBetEventData

- [`0f1dc7d`](https://github.com/BetSwirl/sdk/commit/0f1dc7dfdf4434c84c531c6999b5f8b2d635a929) Thanks [@Kinco-dev](https://github.com/Kinco-dev)! - Add fetch campaign & freebets from clients

## 0.1.2-beta.3

### Patch Changes

- Add getPlaceBetEventData

## 0.1.2-beta.2

### Patch Changes

- Add getWaitRollEventData

## 0.1.2-beta.1

### Patch Changes

- Update fetchFreebetCampaigns return type

## 0.1.2-beta.0

### Patch Changes

- Add place freebet

- Add fetch campaign & freebets from clients

## 0.1.1

### Patch Changes

- Improve getPlaceBetFunctionData params typing

- Add freebet & campaign fetching

## 0.1.0

### Minor Changes

- Add Wheel game with weighted gamr logic

## 0.0.9

### Patch Changes

- Add Keno Game

## 0.0.8

### Patch Changes

- Update encodeInput from CoinToss

## 0.0.7

### Patch Changes

- Update formatCasinoRolledBet

## 0.0.6

### Patch Changes

- Update subgraph urls

## 0.0.5

### Patch Changes

- Add getBetSwirlBetUrl util

- Make options in wait functions optional

- Improve waitRoll return type

- Add getCasinoGamePaused

## 0.0.4

### Patch Changes

- Make walletClient optional from ViemClient and ViemWallet

## 0.0.3

### Patch Changes

- Add Viem client and wallet

## 0.0.2

### Patch Changes

- Update parseRawBetRequirements to include isAllowed

## 0.0.1

### Patch Changes

- Initial release
